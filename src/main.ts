import { Logger, ValidationPipe } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { useContainer } from 'class-validator';
import * as crypto from 'crypto';
import * as mongoSanitize from 'express-mongo-sanitize';
import { rateLimit } from 'express-rate-limit';
import helmet from 'helmet';

import * as session from 'express-session';
import { apiVersion, appPort, sessionSecret, isDevMode } from './app.config';
import { AppModule } from './app.module';
import { AllExceptionsFilter } from './shared/exceptions/all-exceptions.filter';
import { NodeEnv } from './shared/enums/node-env.enum';

async function bootstrap() {
  // Configure CORS origins - handle wildcard "*" properly
  const allowedOrigins = process.env.ALLOWED_ORIGINS?.split(',') || [
    'http://localhost:4200',
    'https://fe-aos-dev.b13devops.com',
    'https://fe-aos-test.b13devops.com',
    'https://fe-aos-uat.b13devops.com',
  ];

  // If ALLOWED_ORIGINS is set to "*", allow all origins
  const corsOrigin = allowedOrigins.length === 1 && allowedOrigins[0].trim() === '*'
    ? true
    : allowedOrigins;

  const app = await NestFactory.create(AppModule, {
    cors: {
      origin: corsOrigin,
      methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS'],
      allowedHeaders: [
        'Origin',
        'X-Requested-With',
        'Content-Type',
        'Accept',
        'Authorization',
        'Cache-Control',
        'Pragma',
      ],
      credentials: true,
      preflightContinue: false,
      optionsSuccessStatus: 204,
    },
  });
  const appVersion = `/api/${apiVersion}`;

  app.useGlobalPipes(
    new ValidationPipe({
      transform: true,
      whitelist: true, // Strip properties that are not defined in the DTO
      forbidNonWhitelisted: true, // Throw error if non-whitelisted properties are present
      disableErrorMessages: process.env.NODE_ENV === NodeEnv.PRODUCTION, // Hide detailed validation errors in production
      validateCustomDecorators: true,
    }),
  );
  app.setGlobalPrefix(appVersion);
  app.use(
    helmet({
      contentSecurityPolicy: {
        directives: {
          defaultSrc: ["'self'"],
          scriptSrc: ["'self'"],
          styleSrc: ["'self'", "'unsafe-inline'"],
          imgSrc: ["'self'", 'data:', 'https:'],
          connectSrc: ["'self'"],
          fontSrc: ["'self'", 'https:', 'data:'],
          objectSrc: ["'none'"],
          mediaSrc: ["'self'"],
          frameSrc: ["'none'"],
        },
      },
      crossOriginEmbedderPolicy: false,
      hsts: {
        maxAge: 31536000,
        includeSubDomains: true,
        preload: true,
      },
      noSniff: true,
      frameguard: { action: 'deny' },
      xssFilter: true,
    }),
  );

  // ---------- Input sanitization ------------------------------------------------
  // `express-mongo-sanitize` (middleware form) is incompatible with Nest 11's new
  // underlying `router` engine because that engine exposes `req.query` as a
  // read-only getter.  Re-implement the sanitisation logic using the library's
  // functional API, which mutates the existing objects instead of re-assigning
  // them. This keeps operator-injection protection without triggering
  // "Cannot set property query ..."

  const sanitize = mongoSanitize.sanitize;

  app.use((req, _res, next) => {
    ['body', 'params', 'headers', 'query'].forEach((key) => {
      const target = (req as any)[key];
      if (target && typeof target === 'object') {
        sanitize(target, { replaceWith: '_' });
      }
    });
    next();
  });

  // Apply rate limiting only in production
  if (process.env.NODE_ENV === NodeEnv.PRODUCTION) {
    app.use(
      rateLimit({
        windowMs: 15 * 60 * 1000, // 15 minutes
        max: 100, // limit each IP to 100 requests per windowMs
        message: {
          error: 'Too many requests from this IP, please try again later.',
          statusCode: 429,
          timestamp: new Date().toISOString(),
        },
        standardHeaders: true,
        legacyHeaders: false,
        skip: (req) => {
          // Skip rate limiting for health checks
          return req.url === '/health' || req.url === '/api/v1/health';
        },
      }),
    );
  }

  app.use(
    session({
      secret: sessionSecret || crypto.randomBytes(32).toString('hex'),
      resave: false,
      saveUninitialized: false,
      cookie: {
        secure: process.env.NODE_ENV === NodeEnv.PRODUCTION,
        httpOnly: true,
        maxAge: 24 * 60 * 60 * 1000, // 24 hours
        sameSite: process.env.NODE_ENV === NodeEnv.PRODUCTION ? 'none' : 'lax',
      },
    }),
  );

  app.useGlobalFilters(new AllExceptionsFilter());

  const options = new DocumentBuilder()
    .setTitle('AOS NestJS')
    .setDescription('AOS API')
    .setVersion('1.0')
    .setBasePath(appVersion)
    .addBearerAuth()
    .build();
  const document = SwaggerModule.createDocument(app, options);
  SwaggerModule.setup('swagger', app, document);

  useContainer(app.select(AppModule), { fallbackOnErrors: true });
  await app.listen(appPort || 3000);

  // Auto-seed default data (demo accounts + test data + resources) on first run
  try {
    const { Seeder } = await import('./features/seed/seed.service');
    const seeder = app.get(Seeder);
    await seeder.importDataDefault();
  } catch (error) {
    Logger.warn('Default data seed failed:', error.message);
  }

  Logger.log('Application started on port ' + (appPort || 3000));
}

bootstrap();
