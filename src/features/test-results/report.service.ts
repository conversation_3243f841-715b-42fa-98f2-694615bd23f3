import { AosCollections, getCollectionToken } from '@aos-database/database.constant';
import { RAGType } from '@aos-enum/rag-type.enum';
import { TestType } from '@aos-enum/test-type.enum';
import { IBDSubtype } from '@aos-enum/ibd-subtype.enum';
import { Inject, Injectable, NotFoundException } from '@nestjs/common';
import * as dayjs from 'dayjs';
import * as xl from 'excel4node';
import { promises as fsPromises } from 'fs';
import { Model } from 'mongoose';
import { filePathReport, seeder } from './../../app.config';
import { UserSchema } from './../../database/schema/user.schema';
import { TestResultStatus } from './../../shared/enums/test-result-status.enum';
import { UserRole } from './../../shared/enums/user-role.enum';
import { ResultInterface, TestResultDetailInterface, TestResultInterface } from './interface/time-up-and-go.interface';
const util = require('util');
const fs = require('fs');

const fontSizeDefault = 10;

@Injectable()
export class ReportService {
  constructor(
    @Inject(getCollectionToken(AosCollections.User))
    private readonly userModel: Model<typeof UserSchema>,
    @Inject(getCollectionToken(AosCollections.TestResult))
    private readonly testResultModel: Model<any>,
  ) {}

  public async downloadReport(query) {
    const filePath = filePathReport + '/Report.xlsx';
    const testType = query.type ? parseInt(query.type) : null;
    await fsPromises.mkdir(filePathReport, { recursive: true });
    let result;

    if (query.patientId) {
      const user = await this.userModel.findById(query.patientId);

      if (!user) {
        throw new NotFoundException('User not found');
      }

      result = await this.getReports([user]);
    } else {
      const users = await this.userModel
        .find({
          role: UserRole.PATIENT,
        })
        .sort({ username: 1 });

      result = await this.getReports(users);
    }

    await this.exportDataToExcel(result, filePath, testType);

    return filePath;
  }

  public async downloadCSVReport(testType: TestType, query) {
    try {
      // Use UTC date for consistent file naming across timezones
      const currentDate = dayjs().utc().format('YYYY-MM-DD');
      const testTypeNames = {
        [TestType.SYMPTOMS]: 'Symptoms',
        [TestType.MENTAL_WELLBEING]: 'Mental_Wellbeing',
        [TestType.GENERAL_WELLBEING]: 'General_Wellbeing',
      };

      // File naming convention: Test1_Symptoms_2025-03-31.csv
      const fileName = `Test${testType}_${testTypeNames[testType]}_${currentDate}.csv`;
      const filePath = filePathReport + '/' + fileName;

      await fsPromises.mkdir(filePathReport, { recursive: true });

      let users;
      if (query.patientId) {
        const user = await this.userModel.findById(query.patientId);
        if (!user) {
          throw new NotFoundException('User not found');
        }
        users = [user];
      } else {
        users = await this.userModel
          .find({
            role: UserRole.PATIENT,
            isArchived: { $ne: true }, // Exclude archived patients
          })
          .sort({ username: 1 });
      }

      if (!users || users.length === 0) {
        throw new NotFoundException('No patients found for CSV generation');
      }

      const csvData = await this.generateCSVData(testType, users);

      if (!csvData || csvData.length === 0) {
        throw new NotFoundException(`No test results found for test type ${testType}`);
      }

      await this.exportDataToCSV(csvData, filePath, testType);

      return fileName;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new Error(`Failed to generate CSV report: ${error.message}`);
    }
  }

  public async downloadIndividualPatientCSV(patientId: string): Promise<string> {
    try {
      // Validate patient exists
      const patient = await this.userModel.findById(patientId);
      if (!patient) {
        throw new NotFoundException('Patient not found');
      }

      // Use UTC date for consistent file naming across timezones
      const currentDate = dayjs().utc().format('YYYY-MM-DD');
      const fileName = `${(patient as any).username}_${currentDate}.csv`;
      const filePath = filePathReport + '/' + fileName;

      await fsPromises.mkdir(filePathReport, { recursive: true });

      // Retrieve all test results for the patient
      const testResults = await this.testResultModel.aggregate([
        {
          $match: {
            patient: patient._id,
            status: { $ne: TestResultStatus.NEW },
          }
        },
        {
          $lookup: {
            from: 'users',
            localField: 'acknowledgedBy',
            foreignField: '_id',
            as: 'acknowledgedBy'
          }
        },
        { $sort: { type: 1, completedDate: -1 } }
      ]);

      // Check if there's any test data available
      const symptomsResults = testResults.filter(tr => tr.type === TestType.SYMPTOMS);
      const mentalWellbeingResults = testResults.filter(tr => tr.type === TestType.MENTAL_WELLBEING);
      const generalWellbeingResults = testResults.filter(tr => tr.type === TestType.GENERAL_WELLBEING);

      // If no test results exist at all, throw error as specified in requirements
      if (testResults.length === 0) {
        throw new Error('There is currently no data available for this report.');
      }

      // Generate the four sheets
      const patientInfoSheet = this.generatePatientInfoSheet(patient);
      const symptomsSheet = this.generateIndividualPatientSymptomsSheet(patient, symptomsResults);
      const mentalWellbeingSheet = this.generateIndividualPatientMentalWellbeingSheet(patient, mentalWellbeingResults);
      const generalWellbeingSheet = this.generateIndividualPatientGeneralWellbeingSheet(patient, generalWellbeingResults);

      // Combine all sheets into a single CSV
      const combinedCSV = this.combineMultiSheetCSV([patientInfoSheet, symptomsSheet, mentalWellbeingSheet, generalWellbeingSheet]);

      // Write to file
      await fsPromises.writeFile(filePath, combinedCSV, 'utf8');

      return fileName;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new Error(`Failed to generate individual patient CSV: ${error.message}`);
    }
  }

  private async generateCSVData(testType: TestType, users) {
    const userIds = users.map(user => user._id);

    // Use aggregation for better performance with large datasets
    const testResults = await this.testResultModel.aggregate([
      {
        $match: {
          patient: { $in: userIds },
          type: testType,
          status: { $ne: TestResultStatus.NEW },
        }
      },
      {
        $lookup: {
          from: 'users',
          localField: 'patient',
          foreignField: '_id',
          as: 'patient'
        }
      },
      {
        $lookup: {
          from: 'users',
          localField: 'acknowledgedBy',
          foreignField: '_id',
          as: 'acknowledgedBy'
        }
      },
      { $unwind: '$patient' },
      {
        $unwind: {
          path: '$acknowledgedBy',
          preserveNullAndEmptyArrays: true
        }
      },
      {
        $match: {
          'patient.isArchived': { $ne: true }
        }
      },
      { $sort: { 'patient.username': 1, completedDate: -1 } }
    ]);

    // Group test results by patient
    const groupedData = this.groupTestResultsByPatient(testResults, testType);

    return groupedData;
  }

  private groupTestResultsByPatient(testResults: any[], testType: TestType) {
    const csvData = [];
    const patientGroups = new Map();

    // Group test results by patient ID
    for (const testResult of testResults) {
      const patientId = testResult.patient._id.toString();
      if (!patientGroups.has(patientId)) {
        patientGroups.set(patientId, {
          patient: testResult.patient,
          results: []
        });
      }
      patientGroups.get(patientId).results.push(testResult);
    }

    // Generate CSV rows with patient grouping
    for (const [, group] of patientGroups) {
      // Add patient header row
      csvData.push(this.createPatientHeaderRow(group.patient));

      // Add column headers for this patient's test results
      csvData.push(this.createTestResultHeaderRow(testType));

      // Add test result rows for this patient (without patient info columns)
      for (const testResult of group.results) {
        if (testType === TestType.SYMPTOMS) {
          csvData.push(this.formatSymptomsCSVRowGrouped(testResult));
        } else if (testType === TestType.MENTAL_WELLBEING) {
          csvData.push(this.formatMentalWellbeingCSVRowGrouped(testResult));
        } else if (testType === TestType.GENERAL_WELLBEING) {
          csvData.push(this.formatGeneralWellbeingCSVRowGrouped(testResult));
        }
      }

      // Add empty row between patients for better readability
      csvData.push({});
    }

    return csvData;
  }

  private async exportDataToCSV(data: any[], filePath: string, testType: TestType) {
    try {
      // For grouped format, we need to manually construct the CSV
      const csvLines = [];

      for (const row of data) {
        if (row.patientHeader) {
          // Patient header row
          csvLines.push(`${row.patientHeader},${row.nhsHeader}`);
        } else if (this.isHeaderRow(row, testType)) {
          // Column headers row
          const headerValues = this.getHeaderValues(testType);
          csvLines.push(headerValues.join(','));
        } else if (Object.keys(row).length === 0) {
          // Empty row between patients
          csvLines.push('');
        } else {
          // Test result data row
          const dataValues = this.getDataValues(row, testType);
          csvLines.push(dataValues.join(','));
        }
      }

      // Write the CSV content to file
      const csvContent = csvLines.join('\n');
      await fsPromises.writeFile(filePath, csvContent, 'utf8');
    } catch (error) {
      throw new Error(`Failed to write CSV file: ${error.message}`);
    }
  }

  private isHeaderRow(row: any, testType: TestType): boolean {
    const headers = this.getCSVHeaders(testType);
    // Check if this row contains header titles rather than data
    return headers.some(header =>
      header.id !== 'username' &&
      header.id !== 'nhsNumber' &&
      row[header.id] === header.title
    );
  }

  private getHeaderValues(testType: TestType): string[] {
    const headers = this.getCSVHeaders(testType);
    const values = [];

    headers.forEach(header => {
      if (header.id !== 'username' && header.id !== 'nhsNumber') {
        values.push(this.escapeCsvValue(header.title));
      }
    });

    return values;
  }

  private getDataValues(row: any, testType: TestType): string[] {
    const headers = this.getCSVHeaders(testType);
    const values = [];

    headers.forEach(header => {
      if (header.id !== 'username' && header.id !== 'nhsNumber') {
        const value = row[header.id] || '';
        values.push(this.escapeCsvValue(value.toString()));
      }
    });

    return values;
  }

  private escapeCsvValue(value: string): string {
    // Escape CSV values that contain commas, quotes, or newlines
    if (value.includes(',') || value.includes('"') || value.includes('\n')) {
      return `"${value.replace(/"/g, '""')}"`;
    }
    return value;
  }

  private getCSVHeaders(testType: TestType) {
    if (testType === TestType.SYMPTOMS) {
      return [
        { id: 'username', title: 'Username' },
        { id: 'nhsNumber', title: 'NHS number' },
        { id: 'dateTime', title: 'Date & Time' },
        { id: 'subtype', title: 'Subtype' },
        { id: 'redFlags', title: 'Red Flags' },
        { id: 'amberFlags', title: 'Amber Flags' },
        { id: 'greenFlags', title: 'Green flags' },
        { id: 'incomplete', title: 'Incomplete' },
        { id: 'flag', title: 'Flag' },
        { id: 'finalScore', title: 'Final Score' },
        { id: 'notes', title: 'Notes' },
        // Dynamic question columns will be added based on IBD subtype
        { id: 'q1', title: 'Q1' },
        { id: 'q2', title: 'Q2' },
        { id: 'q3', title: 'Q3' },
        { id: 'q4', title: 'Q4' },
        { id: 'q5', title: 'Q5' },
        { id: 'q6', title: 'Q6' },
        { id: 'q7', title: 'Q7' },
        { id: 'q8', title: 'Q8' },
      ];
    } else if (testType === TestType.MENTAL_WELLBEING) {
      return [
        { id: 'username', title: 'Username' },
        { id: 'nhsNumber', title: 'NHS number' },
        { id: 'dateTime', title: 'Date & Time' },
        { id: 'incomplete', title: 'Incomplete' },
        { id: 'flag', title: 'Flag' },
        { id: 'hadsAnxietyScore', title: 'HADS Anxiety Score' },
        { id: 'hadsDepressionScore', title: 'HADS Depression Score' },
        { id: 'notes', title: 'Notes' },
      ];
    } else if (testType === TestType.GENERAL_WELLBEING) {
      return [
        { id: 'username', title: 'Username' },
        { id: 'nhsNumber', title: 'NHS number' },
        { id: 'dateTime', title: 'Date & Time' },
        { id: 'incomplete', title: 'Incomplete' },
        { id: 'flag', title: 'Flag' },
        { id: 'overdueFlag', title: 'Overdue Flag' },
        { id: 'sibdqScore', title: 'SIBDQ Score' },
        { id: 'eqScore', title: 'EQ Score' },
        { id: 'notes', title: 'Notes' },
        // Additional blank columns for manual clinician input
        { id: 'week12ScanOutcomes', title: '12 week scan outcomes' },
        { id: 'week20ScanOutcomes', title: '20 week scan outcomes' },
        { id: 'vteDuringPregnancy', title: 'VTE during pregnancy' },
        { id: 'vteProphylaxisGiven', title: 'VTE prophylaxis given' },
        { id: 'birthOutcomes', title: 'Birth outcomes' },
        { id: 'birthWeight', title: 'Birth weight' },
        { id: 'birthOutcomes2', title: 'Birth outcomes' }, // Note: appears twice in requirements
        { id: 'modeOfDelivery', title: 'Mode of delivery' },
        { id: 'complications', title: 'Complications' },
        { id: 'gestationalSize', title: 'Gestational size' },
        { id: 'gestationalAge', title: 'Gestational age' },
        { id: 'ibdOutcomes', title: 'IBD outcomes' },
        { id: 'steroidsDuringPregnancy', title: 'Steroids during pregnancy' },
        { id: 'startedBiologicDuringPregnancy', title: 'Started biologic during pregnancy' },
      ];
    }
    return [];
  }




  // New methods for grouped CSV format
  private createPatientHeaderRow(patient: any) {
    return {
      patientHeader: `${patient.username || ''}`,
      nhsHeader: `NHS: ${patient.nhsNumber || ''}`
    };
  }

  private createTestResultHeaderRow(testType: TestType) {
    const headers = this.getCSVHeaders(testType);
    const headerRow = {};

    // Skip username and nhsNumber columns for grouped format
    headers.forEach((header) => {
      if (header.id !== 'username' && header.id !== 'nhsNumber') {
        headerRow[header.id] = header.title;
      }
    });

    return headerRow;
  }

  private formatSymptomsCSVRowGrouped(testResult: any) {
    const user = testResult.patient;
    const ibdSubtype = user.ibdSubtype === IBDSubtype.CROHNS_DISEASE ? 'HBI' :
                      user.ibdSubtype === IBDSubtype.ULCERATIVE_COLITIS ? 'SCCAI' : '';

    const isIncomplete = testResult.status !== TestResultStatus.COMPLETED;
    const hasFlag = testResult.redFlag || testResult.reds > 0 || testResult.ambers > 0;

    const row = {
      dateTime: testResult.completedDate ? dayjs(testResult.completedDate).utc().format('YYYY-MM-DD HH:mm:ss') : '',
      subtype: ibdSubtype,
      redFlags: testResult.reds || 0,
      amberFlags: testResult.ambers || 0,
      greenFlags: testResult.greens || 0,
      incomplete: isIncomplete ? 'Yes' : 'No',
      flag: hasFlag ? 'Yes' : 'No',
      finalScore: testResult.totalScores || '',
      notes: testResult?.notes || '',
      q1: '', q2: '', q3: '', q4: '', q5: '', q6: '', q7: '', q8: ''
    };

    // Add question answers based on report data with human-readable text conversion
    if (testResult.report && Array.isArray(testResult.report)) {
      const processedQuestions = this.processSymptomsQuestionsForCSV(testResult.report, ibdSubtype);

      // Map processed questions to Q1, Q2, etc. format
      Object.keys(processedQuestions).forEach(questionKey => {
        if (row.hasOwnProperty(questionKey)) {
          row[questionKey] = processedQuestions[questionKey];
        }
      });
    }

    return row;
  }

  /**
   * Process symptoms questions for CSV export with human-readable text conversion
   */
  private processSymptomsQuestionsForCSV(reportItems: any[], ibdSubtype: string): Record<string, string> {
    const processedQuestions: Record<string, string> = {};

    if (ibdSubtype === 'HBI') {
      return this.processHBIQuestionsForCSV(reportItems);
    } else if (ibdSubtype === 'SCCAI') {
      return this.processSCCAIQuestionsForCSV(reportItems);
    }

    return processedQuestions;
  }

  /**
   * Process HBI questions for CSV export
   */
  private processHBIQuestionsForCSV(reportItems: any[]): Record<string, string> {
    const questions: Record<string, string> = {
      q1: '', q2: '', q3: '', q4: '', q5: '', q6: ''
    };

    // Group report items by question code
    const questionGroups = this.groupReportItemsByQuestion(reportItems);

    // Process each HBI question
    questions.q1 = this.convertHBIQ1Answer(questionGroups['HBI_1']);
    questions.q2 = this.convertHBIQ2Answer(questionGroups['HBI_2']);
    questions.q3 = this.convertHBIQ3Answer(questionGroups['HBI_3']);
    questions.q4 = this.convertHBIQ4Answer(questionGroups['HBI_4']);
    questions.q5 = this.convertHBIQ5Answer(questionGroups['HBI_5'], questionGroups['HBI_6']);
    questions.q6 = this.convertHBIQ6Answer(questionGroups['HBI_6']);

    return questions;
  }

  /**
   * Process SCCAI questions for CSV export
   */
  private processSCCAIQuestionsForCSV(reportItems: any[]): Record<string, string> {
    const questions: Record<string, string> = {
      q1: '', q2: '', q3: '', q4: '', q5: '', q6: '', q7: '', q8: ''
    };

    // Group report items by question code
    const questionGroups = this.groupReportItemsByQuestion(reportItems);

    // Process each SCCAI question
    questions.q1 = this.convertSCCAIQ1Answer(questionGroups['SCCAI_1']);
    questions.q2 = this.convertSCCAIQ2Answer(questionGroups['SCCAI_2']);
    questions.q3 = this.convertSCCAIQ3Answer(questionGroups['SCCAI_3']);
    questions.q4 = this.convertSCCAIQ4Answer(questionGroups['SCCAI_4']);
    questions.q5 = this.convertSCCAIQ5Answer(questionGroups['SCCAI_5']);
    questions.q6 = this.convertSCCAIQ6Answer(questionGroups['SCCAI_6']);
    questions.q7 = this.convertSCCAIQ7Answer(questionGroups['SCCAI_7'], questionGroups['SCCAI_8']);
    questions.q8 = this.convertSCCAIQ8Answer(questionGroups['SCCAI_8']);

    return questions;
  }

  /**
   * Group report items by their question code prefix
   */
  private groupReportItemsByQuestion(reportItems: any[]): Record<string, any[]> {
    const groups: Record<string, any[]> = {};

    reportItems.forEach(item => {
      if (item.code) {
        // Extract base question code (e.g., 'HBI_1' from 'HBI_1_A')
        const baseCode = item.code.includes('_') ?
          item.code.split('_').slice(0, 2).join('_') :
          item.code;

        if (!groups[baseCode]) {
          groups[baseCode] = [];
        }
        groups[baseCode].push(item);
      }
    });

    return groups;
  }

  // HBI Question Conversion Methods
  private convertHBIQ1Answer(items: any[]): string {
    if (!items || items.length === 0) return '';
    const item = items[0];
    const codeToText = {
      'A': 'Very well',
      'B': 'Slightly below par',
      'C': 'Poor',
      'D': 'Very poor',
      'E': 'Terrible'
    };
    return codeToText[item.answer] || item.answer || '';
  }

  private convertHBIQ2Answer(items: any[]): string {
    if (!items || items.length === 0) return '';
    const item = items[0];
    const codeToText = {
      'A': 'None',
      'B': 'Mild',
      'C': 'Moderate',
      'D': 'Severe'
    };
    return codeToText[item.answer] || item.answer || '';
  }

  private convertHBIQ3Answer(items: any[]): string {
    if (!items || items.length === 0) return '';
    const item = items[0];
    const codeToText = {
      'A': '0',
      'B': '1',
      'C': '2',
      'D': '3',
      'E': '4',
      'F': '5+'
    };
    return codeToText[item.answer] || item.answer || '';
  }

  private convertHBIQ4Answer(items: any[]): string {
    if (!items || items.length === 0) return '';

    const codeToText = {
      'A': 'Arthralgia (joint pain)',
      'B': 'Uveitis (eye inflammation)',
      'C': 'Erythema nodosum (painful rash on legs)',
      'D': 'Aphthous ulcers (type of mouth ulcer)',
      'E': 'Pyoderma gangrenosum (type of necrotic skin ulcer)',
      'F': 'Anal fissures (anal tear)',
      'G': 'New fistula (channel between bowel and skin)',
      'H': 'Abscess (pus-filled)'
    };

    // Multiple answers possible for checkbox type - use pipe separator
    const answers = items.map(item => codeToText[item.answer] || item.answer).filter(Boolean);
    return answers.join(' | ');
  }

  private convertHBIQ5Answer(q5Items: any[], q6Items: any[]): string {
    if (!q5Items || q5Items.length === 0) return '';
    const item = q5Items[0];

    let result = '';
    if (item.answer === 'Yes' || item.answer === 'No') {
      result = item.answer;

      // Add date if available
      if (item.testDate) {
        const formattedDate = dayjs(item.testDate).format('YYYY-MM-DD');
        result += ` (${formattedDate})`;
      }
    } else {
      result = item.answer || '';
    }

    return result;
  }

  private convertHBIQ6Answer(items: any[]): string {
    if (!items || items.length === 0) return '';
    const item = items[0];

    // For numeric input, show the actual numeric value
    if (item.numericValue !== undefined && item.numericValue !== null) {
      return item.numericValue.toString();
    }

    return item.answer || item.score?.toString() || '';
  }

  // SCCAI Question Conversion Methods
  private convertSCCAIQ1Answer(items: any[]): string {
    if (!items || items.length === 0) return '';
    const item = items[0];
    const codeToText = {
      'A': '0-3',
      'B': '4-6',
      'C': '7-9',
      'D': '9+'
    };
    return codeToText[item.answer] || item.answer || '';
  }

  private convertSCCAIQ2Answer(items: any[]): string {
    if (!items || items.length === 0) return '';
    const item = items[0];
    const codeToText = {
      'A': '0',
      'B': '1-3',
      'C': '4+'
    };
    return codeToText[item.answer] || item.answer || '';
  }

  private convertSCCAIQ3Answer(items: any[]): string {
    if (!items || items.length === 0) return '';
    const item = items[0];
    const codeToText = {
      'A': 'Not urgent',
      'B': 'Hurried',
      'C': 'Immediately urgent',
      'D': 'No warning, incontinence'
    };
    return codeToText[item.answer] || item.answer || '';
  }

  private convertSCCAIQ4Answer(items: any[]): string {
    if (!items || items.length === 0) return '';
    const item = items[0];
    const codeToText = {
      'A': 'None',
      'B': 'Trace',
      'C': 'Occasionally frank',
      'D': 'Usually frank'
    };
    return codeToText[item.answer] || item.answer || '';
  }

  private convertSCCAIQ5Answer(items: any[]): string {
    if (!items || items.length === 0) return '';
    const item = items[0];
    const codeToText = {
      'A': 'Very well',
      'B': 'Slightly below par',
      'C': 'Poor',
      'D': 'Very poor',
      'E': 'Terrible'
    };
    return codeToText[item.answer] || item.answer || '';
  }

  private convertSCCAIQ6Answer(items: any[]): string {
    if (!items || items.length === 0) return '';

    const codeToText = {
      'A': 'Arthralgia (joint pain)',
      'B': 'Uveitis (eye inflammation)',
      'C': 'Erythema nodosum (painful rash on legs)',
      'D': 'Aphthous ulcers (type of mouth ulcer)',
      'E': 'Pyoderma gangrenosum (type of necrotic skin ulcer)',
      'F': 'Anal fissures (anal tear)',
      'G': 'New fistula (channel between bowel and skin)',
      'H': 'Abscess (pus-filled)'
    };

    // Multiple answers possible for checkbox type - use pipe separator
    const answers = items.map(item => codeToText[item.answer] || item.answer).filter(Boolean);
    return answers.join(' | ');
  }

  private convertSCCAIQ7Answer(q7Items: any[], q8Items: any[]): string {
    if (!q7Items || q7Items.length === 0) return '';
    const item = q7Items[0];

    let result = '';
    if (item.answer === 'Yes' || item.answer === 'No') {
      result = item.answer;

      // Add date if available
      if (item.testDate) {
        const formattedDate = dayjs(item.testDate).format('YYYY-MM-DD');
        result += ` (${formattedDate})`;
      }
    } else {
      result = item.answer || '';
    }

    return result;
  }

  private convertSCCAIQ8Answer(items: any[]): string {
    if (!items || items.length === 0) return '';
    const item = items[0];

    // For numeric input, show the actual numeric value
    if (item.numericValue !== undefined && item.numericValue !== null) {
      return item.numericValue.toString();
    }

    return item.answer || item.score?.toString() || '';
  }

  /**
   * Process General Wellbeing questions for CSV export with human-readable text conversion
   */
  private processGeneralWellbeingQuestionsForCSV(reportItems: any[]): Record<string, string> {
    const questions: Record<string, string> = {};

    // Initialize all question slots
    for (let i = 1; i <= 37; i++) {
      questions[`q${i}`] = '';
    }

    // Group report items by question code
    const questionGroups = this.groupReportItemsByQuestion(reportItems);

    // Process each general wellbeing question
    // Based on the test data structure, we need to handle GW_1 through GW_37
    for (let i = 1; i <= 37; i++) {
      const questionCode = `GW_${i}`;
      const questionKey = `q${i}`;

      if (questionGroups[questionCode]) {
        questions[questionKey] = this.convertGeneralWellbeingAnswer(questionGroups[questionCode], i);
      }
    }

    return questions;
  }

  /**
   * Convert General Wellbeing answers to human-readable text
   */
  private convertGeneralWellbeingAnswer(items: any[], questionNumber: number): string {
    if (!items || items.length === 0) return '';

    // Special handling for specific questions
    if (questionNumber === 1 || questionNumber === 5) {
      // Q1 and Q5: Display the input text value (not codes)
      const item = items[0];
      return item.textValue || item.answer || '';
    } else if (questionNumber === 12) {
      // Q12: Display the numeric value
      const item = items[0];
      if (item.numericValue !== undefined && item.numericValue !== null) {
        return item.numericValue.toString();
      }
      return item.answer || item.score?.toString() || '';
    } else {
      // For other questions, return the answer code or text as is
      // Multiple answers possible for checkbox type - use pipe separator
      if (items.length > 1) {
        const answers = items.map(item => item.answer || item.textValue || '').filter(Boolean);
        return answers.join(' | ');
      } else {
        const item = items[0];
        return item.answer || item.textValue || item.score?.toString() || '';
      }
    }
  }

  private formatMentalWellbeingCSVRowGrouped(testResult: any) {
    const isIncomplete = testResult.status !== TestResultStatus.COMPLETED;
    const hasFlag = testResult.redFlag || testResult.anxietyScore >= 11 || testResult.depressionScore >= 11;

    return {
      dateTime: testResult.completedDate ? dayjs(testResult.completedDate).utc().format('YYYY-MM-DD HH:mm:ss') : '',
      incomplete: isIncomplete ? 'Yes' : 'No',
      flag: hasFlag ? 'Yes' : 'No',
      hadsAnxietyScore: testResult.anxietyScore || '',
      hadsDepressionScore: testResult.depressionScore || '',
      notes: testResult?.notes || '',
    };
  }

  private formatGeneralWellbeingCSVRowGrouped(testResult: any) {
    const isIncomplete = testResult.status !== TestResultStatus.COMPLETED;
    const hasFlag = testResult.redFlag;
    const isOverdue = testResult.status === TestResultStatus.OVERDUE;

    return {
      dateTime: testResult.completedDate ? dayjs(testResult.completedDate).utc().format('YYYY-MM-DD HH:mm:ss') : '',
      incomplete: isIncomplete ? 'Yes' : 'No',
      flag: hasFlag ? 'Yes' : 'No',
      overdueFlag: isOverdue ? 'Yes' : 'No',
      sibdqScore: testResult.sibdqScore || '',
      eqScore: testResult.eqScore || '',
      notes: testResult?.notes || '',
      // Fix for Valid Defect #2: Add all missing manual input columns as blank fields
      week12ScanOutcomes: '',
      week20ScanOutcomes: '',
      vteDuringPregnancy: '',
      vteProphylaxisGiven: '',
      birthOutcomes: '',
      birthWeight: '',
      birthOutcomes2: '', // Note: appears twice in requirements
      modeOfDelivery: '',
      complications: '',
      gestationalSize: '',
      gestationalAge: '',
      ibdOutcomes: '',
      steroidsDuringPregnancy: '',
      startedBiologicDuringPregnancy: '',
    };
  }

  private async getReports(users) {
    const result = {
      timeUpAndGo: [] as TestResultInterface[],
      symptoms: [] as TestResultInterface[],
      healthStatus: [] as TestResultInterface[],
    } as ResultInterface;

    for (const user of users) {
      const records = await this.fetchUserTestRecords(user._id);
      const categorizedResults = this.categorizeTestResults(records);
      this.addUserToResults(result, user, categorizedResults);
    }

    return result;
  }

  private async fetchUserTestRecords(userId: string) {
    return await this.testResultModel
      .find({
        patient: userId,
        status: { $ne: TestResultStatus.NEW },
      })
      .populate('acknowledgedBy')
      .sort({ createdAt: -1 });
  }

  private categorizeTestResults(records: any[]) {
    const timeUpAndGoResults = [] as TestResultDetailInterface[];
    const symptomsResults = [] as TestResultDetailInterface[];
    const healthStatusResults = [] as TestResultDetailInterface[];

    for (const record of records) {
      if (this.shouldSkipRecord(record)) {
        continue;
      }

      this.categorizeRecord(record, timeUpAndGoResults, symptomsResults, healthStatusResults);
    }

    return {
      timeUpAndGoResults,
      symptomsResults,
      healthStatusResults,
    };
  }

  private shouldSkipRecord(record: any): boolean {
    return (
      record.status === TestResultStatus.OVERDUE &&
      record.overDueCount != undefined &&
      record.overDueCount !== 0
    );
  }

  private categorizeRecord(
    record: any,
    timeUpAndGoResults: TestResultDetailInterface[],
    symptomsResults: TestResultDetailInterface[],
    healthStatusResults: TestResultDetailInterface[]
  ) {
    if (record.type === TestType.SYMPTOMS) {
      symptomsResults.push(record as any);
    } else if (record.type === TestType.MENTAL_WELLBEING) {
      timeUpAndGoResults.push(record as any);
    } else {
      healthStatusResults.push(record as any);
    }
  }

  private addUserToResults(result: ResultInterface, user: any, categorizedResults: any) {
    const userInfo = this.createUserInfo(user);

    result.timeUpAndGo.push({
      ...userInfo,
      records: categorizedResults.timeUpAndGoResults,
    });

    result.symptoms.push({
      ...userInfo,
      records: categorizedResults.symptomsResults,
    });

    result.healthStatus.push({
      ...userInfo,
      records: categorizedResults.healthStatusResults,
    });
  }

  private createUserInfo(user: any) {
    return {
      username: user.username,
      fullName: user.firstName + ' ' + user.lastName,
      nhsNumber: user.nhsNumber,
    };
  }

  private async exportDataToExcel(data: ResultInterface, path, testType = null) {
    const wb = new xl.Workbook();
    wb.writeP = util.promisify(wb.write);
    const thinBorderStyle = wb.createStyle({
      border: {
        left: {
          style: 'thin',
          color: 'black',
        },
        right: {
          style: 'thin',
          color: 'black',
        },
        top: {
          style: 'thin',
          color: 'black',
        },
        bottom: {
          style: 'thin',
          color: 'black',
        },
        outline: false,
      },
    });

    if (testType === null) {
      this.createSymptomsSheet(data, wb, thinBorderStyle);
      this.createTimedUpAndGoSheet(data, wb, thinBorderStyle);
      this.createHealthStatusSheet(data, wb, thinBorderStyle);
    } else if (testType === TestType.SYMPTOMS) {
      this.createSymptomsSheet(data, wb, thinBorderStyle);
    } else if (testType === TestType.MENTAL_WELLBEING) {
      this.createTimedUpAndGoSheet(data, wb, thinBorderStyle);
    } else if (testType === TestType.GENERAL_WELLBEING) {
      this.createHealthStatusSheet(data, wb, thinBorderStyle);
    }

    await wb.writeP(path);
  }

  private createSymptomsSheet(data: ResultInterface, wb, thinBorderStyle) {
    const sheet = wb.addWorksheet('Symptoms Test Report');
    let row = 1;

    sheet
      .cell(row, 1)
      .string('Annotation')
      .style({ font: { bold: true, size: 16 } });
    row = row + 2;
    row = this.writeQuestionSymptoms(sheet, row);
    row = row + 1;

    data.symptoms.forEach((element) => {
      row = this.createSymptomsPatientSection(sheet, row, element);
      row = this.createSymptomsHeaders(sheet, row, thinBorderStyle);
      row = this.processSymptomsRecords(sheet, row, element, thinBorderStyle);
      row += 2;
    });
  }

  private createSymptomsPatientSection(sheet, row: number, element: any): number {
    sheet
      .cell(row, 1)
      .string(`Patient: ${element.username} (${element.nhsNumber})`)
      .style({ font: { bold: true, size: 20 } });
    row = row + 1;
    sheet
      .cell(row, 1)
      .string('Symptoms Test')
      .style({ font: { bold: true, size: 20 } });
    row = row + 2;
    return row;
  }

  private createSymptomsHeaders(sheet, row: number, thinBorderStyle): number {
    const headers = [
      'Username', 'Date & Time', 'Q1', 'Q1b', 'Q2', 'Q2b', 'Q2c', 'Q3', 'Q3b', 
      'Q4', 'Q5', 'Q6', 'Q6b', 'Q7', 'Red', 'Amber', 'Green', 'Incomplete', 'Flag?', 'Notes'
    ];

    headers.forEach((header, index) => {
      sheet
        .cell(row, index + 1)
        .string(header)
        .style(thinBorderStyle)
        .style({
          font: { bold: true, size: fontSizeDefault },
          alignment: { horizontal: 'center' },
        });
    });

    return row + 1;
  }

  private processSymptomsRecords(sheet, row: number, element: any, thinBorderStyle): number {
    element.records.forEach((record) => {
      row = this.writeSymptomsRecordData(sheet, row, element, record, thinBorderStyle);
      row = this.writeSymptomsQuestions(sheet, row, record, thinBorderStyle);
      row = this.writeSymptomsRAGCounts(sheet, row, record, thinBorderStyle);
      row = this.writeSymptomsMetadata(sheet, row, record, thinBorderStyle);
      row++;
    });
    return row;
  }

  private writeSymptomsRecordData(sheet, row: number, element: any, record: any, thinBorderStyle): number {
    let currentColumn = 1;
    const startDate = record.startDate !== undefined ? dayjs(record.startDate).format('DD/MM/YYYY') : '';

    sheet
      .cell(row, currentColumn++)
      .string(element.username)
      .style(thinBorderStyle)
      .style({
        font: { size: fontSizeDefault },
        alignment: { horizontal: 'center' },
      });

    sheet
      .cell(row, currentColumn++)
      .string(startDate + (record.status !== undefined && record.status === TestResultStatus.CANCELLED ? '*' : ''))
      .style(thinBorderStyle)
      .style({
        font: { size: fontSizeDefault },
        alignment: { horizontal: 'center' },
      });

    return currentColumn;
  }

  private writeSymptomsQuestions(sheet, row: number, record: any, thinBorderStyle): number {
    let currentColumn = 3; // Starting after username and date columns
    let totalReport = 12; // default if report = [] (array empty)
    if (record.report?.length > 0) {
      totalReport += 2;
    } // Q2 has 3 sub questions but only 1 question
    
    for (let i = 0; i < totalReport; i++) {
      const reportItem = record.report[i];

      if (reportItem?.code?.toLowerCase() === 'q3') {
        currentColumn++;
      }
      sheet
        .cell(row, currentColumn++)
        .string(reportItem?.answer || '')
        .style(thinBorderStyle)
        .style({
          font: { size: fontSizeDefault },
          alignment: { horizontal: 'center' },
        });
      if (reportItem?.code?.toLowerCase()?.includes('q2c')) {
        if (record.report?.length > 0) {
          currentColumn--;
        }
      }
    }
    return currentColumn;
  }

  private writeSymptomsRAGCounts(sheet, row: number, record: any, thinBorderStyle): number {
    const ragCounts = this.calculateRAGCounts(record.report);
    let currentColumn = 15; // Starting after question columns

    sheet
      .cell(row, currentColumn++)
      .number(ragCounts.red)
      .style(thinBorderStyle)
      .style({
        font: { size: fontSizeDefault },
        alignment: { horizontal: 'center' },
      });

    sheet
      .cell(row, currentColumn++)
      .number(ragCounts.amber)
      .style(thinBorderStyle)
      .style({
        font: { size: fontSizeDefault },
        alignment: { horizontal: 'center' },
      });

    sheet
      .cell(row, currentColumn++)
      .number(ragCounts.green)
      .style(thinBorderStyle)
      .style({
        font: { size: fontSizeDefault },
        alignment: { horizontal: 'center' },
      });

    return currentColumn;
  }

  private calculateRAGCounts(report: any[]): { red: number, amber: number, green: number } {
    let red = 0;
    let amber = 0;
    let green = 0;

    report.forEach((reportItem) => {
      if (reportItem.rag === RAGType.A) {
        amber += 1;
      } else if (reportItem.rag === RAGType.G) {
        green += 1;
      } else if (reportItem.rag === RAGType.R) {
        red += 1;
      }
    });

    return { red, amber, green };
  }

  private writeSymptomsMetadata(sheet, row: number, record: any, thinBorderStyle): number {
    let currentColumn = 18; // Starting after RAG columns

    // Incomplete
    sheet
      .cell(row, currentColumn++)
      .string(record.status !== undefined && record.status === TestResultStatus.CANCELLED ? 'Yes' : '')
      .style(thinBorderStyle)
      .style({
        font: { size: fontSizeDefault },
        alignment: { horizontal: 'center' },
      });

    // Flag
    sheet
      .cell(row, currentColumn++)
      .string(record.redFlag ? 'Yes' : '')
      .style(thinBorderStyle)
      .style({
        font: { size: fontSizeDefault },
        alignment: { horizontal: 'center' },
      });

    // Notes
    sheet
      .cell(row, currentColumn++)
      .string(this.formatNotesWithAcknowledgedBy(record, '\r\n'))
      .style(thinBorderStyle)
      .style({
        font: { size: fontSizeDefault },
        alignment: { horizontal: 'center' },
      });

    return currentColumn;
  }

  private createTimedUpAndGoSheet(data: ResultInterface, wb, thinBorderStyle) {
    const sheet = wb.addWorksheet('Timed Up and Go Test Report');
    let row = 1;

    data.timeUpAndGo.forEach((element) => {
      row = this.createTimedUpAndGoPatientSection(sheet, row, element);
      row = this.createTimedUpAndGoHeaders(sheet, row, thinBorderStyle);
      row = this.processTimedUpAndGoRecords(sheet, row, element, thinBorderStyle);
      row = row + 2;
    });
  }

  private createTimedUpAndGoPatientSection(sheet, row: number, element: any): number {
    sheet
      .cell(row, 1)
      .string(`Patient: ${element.username} (${element.nhsNumber})`)
      .style({ font: { bold: true, size: 20 } });
    row = row + 1;
    sheet
      .cell(row, 1)
      .string('Timed Up & Go Test')
      .style({ font: { bold: true, size: 20 } });
    row = row + 2;
    sheet
      .cell(row, 1)
      .string('Test Report')
      .style({ font: { bold: true, size: 16 } });
    row = row + 1;
    return row;
  }

  private createTimedUpAndGoHeaders(sheet, row: number, thinBorderStyle): number {
    const headers = [
      { text: 'Username', size: fontSizeDefault },
      { text: 'Date & Time', size: undefined },
      { text: 'Time', size: undefined },
      { text: 'Cancelled', size: undefined },
      { text: 'Flag?', size: undefined },
      { text: 'Notes', size: undefined },
    ];

    headers.forEach((header, index) => {
      sheet
        .cell(row, index + 1)
        .string(header.text)
        .style(thinBorderStyle)
        .style({
          font: { bold: true, size: header.size },
          alignment: { horizontal: 'center' },
        });
    });

    return row + 1;
  }

  private processTimedUpAndGoRecords(sheet, row: number, element: any, thinBorderStyle): number {
    element.records.forEach((record) => {
      row = this.writeTimedUpAndGoRecordData(sheet, row, element, record, thinBorderStyle);
      row++;
    });
    return row;
  }

  private writeTimedUpAndGoRecordData(sheet, row: number, element: any, record: any, thinBorderStyle): number {
    let currentColumn = 1;
    const startDate = record.startDate !== undefined ? dayjs(record.startDate).format('DD/MM/YYYY') : '';

    sheet
      .cell(row, currentColumn++)
      .string(element.username)
      .style(thinBorderStyle)
      .style({
        font: { size: fontSizeDefault },
        alignment: { horizontal: 'center' },
      });

    sheet
      .cell(row, currentColumn++)
      .string(startDate + (record.status !== undefined && record.status === TestResultStatus.CANCELLED ? '*' : ''))
      .style(thinBorderStyle);

    // ttc
    sheet
      .cell(row, currentColumn++)
      .number(record.ttc || 0)
      .style({ alignment: { horizontal: 'center' } })
      .style(thinBorderStyle);

    // Incomplete
    sheet
      .cell(row, currentColumn++)
      .string(record.status !== undefined && record.status === TestResultStatus.CANCELLED ? 'Yes' : '')
      .style({ alignment: { horizontal: 'center' } })
      .style(thinBorderStyle);

    // Flag
    sheet
      .cell(row, currentColumn++)
      .string(record.redFlag ? 'Yes' : '')
      .style({ alignment: { horizontal: 'center' } })
      .style(thinBorderStyle);

    // Notes
    sheet
      .cell(row, currentColumn++)
      .string(this.formatNotesWithAcknowledgedBy(record))
      .style(thinBorderStyle);

    return row;
  }

  private createHealthStatusSheet(data: ResultInterface, wb, thinBorderStyle) {
    const sheet = wb.addWorksheet('KCCQ Test Report');
    let row = 1;

    sheet
      .cell(row, 1)
      .string('Annotation')
      .style({ font: { bold: true, size: 16 } });
    row = row + 2;
    row = this.writeQuestionHealthStatus(sheet, row);
    row = row + 1;

    data.healthStatus.forEach((element) => {
      row = this.createHealthStatusPatientSection(sheet, row, element);
      row = this.createHealthStatusHeaders(sheet, row, thinBorderStyle);
      row = this.processHealthStatusRecords(sheet, row, element, thinBorderStyle);
      row += 2;
    });
  }

  private createHealthStatusPatientSection(sheet, row: number, element: any): number {
    sheet
      .cell(row, 1)
      .string(`Patient: ${element.username} (${element.nhsNumber})`)
      .style({ font: { bold: true, size: 20 } });
    row = row + 1;
    sheet
      .cell(row, 1)
      .string('KCCQ Test')
      .style({ font: { bold: true, size: 20 } });
    row = row + 2;
    return row;
  }

  private createHealthStatusHeaders(sheet, row: number, thinBorderStyle): number {
    const headers = [
      'Username', 'Date & Time', 'Q1A', 'Q1B', 'Q1C', 'Q1D', 'Q1E', 'Q1F', 'Q2', 'Q3', 'Q4', 'Q5', 
      'Q6', 'Q7', 'Q8', 'Q9', 'Q10', 'Q11', 'Q12', 'Q13', 'Q14', 'Q15A', 'Q15B', 'Q15C', 'Q15D', 
      'Score', 'Incomplete', 'Flag?', 'Notes'
    ];

    headers.forEach((header, index) => {
      sheet
        .cell(row, index + 1)
        .string(header)
        .style(thinBorderStyle)
        .style({
          font: { bold: true, size: fontSizeDefault },
          alignment: { horizontal: 'center' },
        });
    });

    return row + 1;
  }

  private processHealthStatusRecords(sheet, row: number, element: any, thinBorderStyle): number {
    element.records.forEach((record) => {
      row = this.writeHealthStatusRecordData(sheet, row, element, record, thinBorderStyle);
      row = this.writeHealthStatusQuestions(sheet, row, record, thinBorderStyle);
      row = this.writeHealthStatusMetadata(sheet, row, record, thinBorderStyle);
      row++;
    });
    return row;
  }

  private writeHealthStatusRecordData(sheet, row: number, element: any, record: any, thinBorderStyle): number {
    let currentColumn = 1;
    const startDate = record.startDate !== undefined ? dayjs(record.startDate).format('DD/MM/YYYY') : '';

    sheet
      .cell(row, currentColumn++)
      .string(element.username)
      .style(thinBorderStyle)
      .style({
        font: { size: fontSizeDefault },
        alignment: { horizontal: 'center' },
      });

    sheet
      .cell(row, currentColumn++)
      .string(startDate + (record.status !== undefined && record.status === TestResultStatus.CANCELLED ? '*' : ''))
      .style(thinBorderStyle)
      .style({
        font: { size: fontSizeDefault },
        alignment: { horizontal: 'center' },
      });

    return currentColumn;
  }

  private writeHealthStatusQuestions(sheet, row: number, record: any, thinBorderStyle): number {
    let currentColumn = 3; // Starting after username and date columns
    const totalReport = 23; // default if report = [] (array empty)
    
    for (let i = 0; i < totalReport; i++) {
      const reportItem = record.report[i];

      sheet
        .cell(row, currentColumn++)
        .number(reportItem?.score || 0)
        .style(thinBorderStyle)
        .style({
          font: { size: fontSizeDefault },
          alignment: { horizontal: 'center' },
        });
    }

    sheet
      .cell(row, currentColumn++)
      .number(record.totalScores || 0)
      .style(thinBorderStyle)
      .style({
        font: { size: fontSizeDefault },
        alignment: { horizontal: 'center' },
      });

    return currentColumn;
  }

  private writeHealthStatusMetadata(sheet, row: number, record: any, thinBorderStyle): number {
    let currentColumn = 26; // Starting after score columns

    // Incomplete
    sheet
      .cell(row, currentColumn++)
      .string(record.status !== undefined && record.status === TestResultStatus.CANCELLED ? 'Yes' : '')
      .style(thinBorderStyle)
      .style({
        font: { size: fontSizeDefault },
        alignment: { horizontal: 'center' },
      });

    // Flag
    sheet
      .cell(row, currentColumn++)
      .string(record.redFlag ? 'Yes' : '')
      .style(thinBorderStyle)
      .style({
        font: { size: fontSizeDefault },
        alignment: { horizontal: 'center' },
      });

    // Notes
    sheet
      .cell(row, currentColumn++)
      .string(this.formatNotesWithAcknowledgedBy(record))
      .style(thinBorderStyle)
      .style({
        font: { size: fontSizeDefault },
        alignment: { horizontal: 'center' },
      });

    return currentColumn;
  }

  private writeQuestionSymptoms(sheet, rowCurrent: number) {
    const alphabets = 'abcdefghijklmnopqrstuvwxyz'.split('');
    let row = rowCurrent;
    const questions = this.getSymptomsQuestions();

    for (const question of questions) {
      row = this.writeSymptomsQuestion(sheet, row, question);
      row = this.writeSymptomsAnswers(sheet, row, question.answers, alphabets);
      row = row + 1;
    }

    return row;
  }

  private getSymptomsQuestions() {
    const rawdata = fs.readFileSync(seeder.testDataPath);
    const records = JSON.parse(rawdata) as any[];
    return records.filter((record) => {
      return record.title === 'Symptoms';
    })[0]?.questions as Array<{
      text: string;
      code: string;
      answers: Array<{
        questions: Array<{
          text: string;
          code: string;
          answers: Array<{ text: string; code: string; questions: any[] }>;
        }>;
      }>;
    }>;
  }

  private writeSymptomsQuestion(sheet, row: number, question: any): number {
    sheet
      .cell(row, 1)
      .string(question.code)
      .style({ font: { bold: true, size: fontSizeDefault } });
    
    sheet
      .cell(row, 4)
      .string(question.text)
      .style({ font: { bold: true, size: fontSizeDefault } });
    
    return row + 1;
  }

  private writeSymptomsAnswers(sheet, row: number, answers: any[], alphabets: string[]): number {
    for (const answer of answers) {
      const subQuestions = answer.questions || [];
      if (subQuestions.length === 0) {
        continue;
      }
      
      row = this.writeSymptomsSubQuestions(sheet, row, subQuestions, alphabets);
    }

    return row;
  }

  private writeSymptomsSubQuestions(sheet, row: number, subQuestions: any[], alphabets: string[]): number {
    let subQuestionIndex = 0;
    
    for (const subQuestion of subQuestions) {
      const subAnswers = subQuestion.answers || [];

      if (subAnswers.length === 0) {
        continue;
      }

      row = this.writeSymptomsSubQuestion(sheet, row, subQuestion, alphabets[subQuestionIndex]);
      row = this.writeSymptomsSubAnswers(sheet, row, subAnswers, subQuestions);
      subQuestionIndex++;
    }

    return row;
  }

  private writeSymptomsSubQuestion(sheet, row: number, subQuestion: any, alphabet: string): number {
    sheet
      .cell(row, 2)
      .string(alphabet)
      .style({ font: { size: fontSizeDefault } });
    
    sheet
      .cell(row, 4)
      .string(subQuestion.text)
      .style({ font: { size: fontSizeDefault } });
    
    return row + 1;
  }

  private writeSymptomsSubAnswers(sheet, row: number, subAnswers: any[], subQuestions: any[]): number {
    for (const subAnswerIndex in subAnswers) {
      const subAnswer = subAnswers[subAnswerIndex];
      
      this.handleSubSubQuestions(subAnswer, subAnswerIndex, subQuestions);
      row = this.writeSymptomsSubAnswer(sheet, row, subAnswer);
    }

    return row;
  }

  private handleSubSubQuestions(subAnswer: any, subAnswerIndex: string, subQuestions: any[]): void {
    const subSubQuestion = subAnswer.questions || [];

    if (+subAnswerIndex === 0 && subSubQuestion.length !== 0) {
      subQuestions.push(subAnswer.questions[0]);
    }
  }

  private writeSymptomsSubAnswer(sheet, row: number, subAnswer: any): number {
    sheet
      .cell(row, 3)
      .string(subAnswer.code)
      .style({ font: { size: fontSizeDefault } });
    
    sheet
      .cell(row, 4)
      .string(this.cleanSymptomsAnswerText(subAnswer.text))
      .style({ font: { size: fontSizeDefault } });
    
    return row + 1;
  }

  private cleanSymptomsAnswerText(text: string): string {
    return text
      .replace(/<b>/g, '')
      .replace(/<\/b>/g, '')
      .replace(/<br\/>/g, '');
  }

  private writeQuestionHealthStatus(sheet, rowCurrent: number) {
    const alphabets = 'abcdefghijklmnopqrstuvwxyz'.split('');
    let row = rowCurrent;
    const rawdata = fs.readFileSync(seeder.testDataPath);
    const records = JSON.parse(rawdata) as any[];
    const questions = this.getHealthStatusQuestions(records);

    for (const questionIndex in questions) {
      const question = questions[questionIndex];
      row = this.writeQuestion(sheet, row, question, +questionIndex + 1);
      row = this.writeQuestionContent(sheet, row, question, alphabets);
      row = row + 1;
    }

    return row;
  }

  private getHealthStatusQuestions(records: any[]) {
    return (records.filter((record) => {
      return record.title === 'Health Status';
    })[0]?.questions || []) as Array<{
      text: string;
      questions: Array<{
        text: string;
        code: string;
        answers: Array<{ text: string; score: number }>;
      }>;
      answers: Array<{ text: string; score: number }>;
    }>;
  }

  private writeQuestion(sheet, row: number, question: any, questionNumber: number): number {
    sheet
      .cell(row, 1)
      .string(`Q${questionNumber}`)
      .style({ font: { bold: true, size: fontSizeDefault } });
    
    sheet
      .cell(row, 4)
      .string(this.cleanQuestionText(question.text))
      .style({ font: { bold: true, size: fontSizeDefault } });
    
    return row + 1;
  }

  private writeQuestionContent(sheet, row: number, question: any, alphabets: string[]): number {
    const subQuestions = question.questions || [];
    const answers = question.answers || [];

    if (subQuestions.length === 0 && answers.length === 0) {
      return row;
    }

    if (subQuestions.length !== 0) {
      return this.writeSubQuestions(sheet, row, subQuestions, alphabets);
    }

    if (answers.length !== 0) {
      return this.writeAnswers(sheet, row, answers);
    }

    return row;
  }

  private writeSubQuestions(sheet, row: number, subQuestions: any[], alphabets: string[]): number {
    for (const subQuestionIndex in subQuestions) {
      const subQuestion = subQuestions[subQuestionIndex];
      
      sheet
        .cell(row, 2)
        .string(alphabets[subQuestionIndex].toUpperCase())
        .style({ font: { size: fontSizeDefault } });
      
      sheet
        .cell(row, 4)
        .string(subQuestion.text)
        .style({ font: { size: fontSizeDefault } });
      
      row = row + 1;

      const isLastSubQuestion = +subQuestionIndex === subQuestions.length - 1;
      if (isLastSubQuestion) {
        row = this.writeSubAnswers(sheet, row, subQuestion.answers || []);
      }
    }

    return row;
  }

  private writeSubAnswers(sheet, row: number, subAnswers: any[]): number {
    if (subAnswers.length === 0) {
      return row;
    }

    for (const subAnswerIndex in subAnswers) {
      const subAnswer = subAnswers[subAnswerIndex];
      
      sheet
        .cell(row, 3)
        .number(+subAnswerIndex + 1)
        .style({
          font: { size: fontSizeDefault },
          alignment: { horizontal: 'left' },
        });
      
      sheet
        .cell(row, 4)
        .string(this.cleanAnswerText(subAnswer.text))
        .style({ font: { size: fontSizeDefault } });
      
      row = row + 1;
    }

    return row;
  }

  private writeAnswers(sheet, row: number, answers: any[]): number {
    for (const answerIndex in answers) {
      const answer = answers[answerIndex];
      
      sheet
        .cell(row, 3)
        .number(+answerIndex + 1)
        .style({
          font: { size: fontSizeDefault },
          alignment: { horizontal: 'left' },
        });
      
      sheet
        .cell(row, 4)
        .string(this.cleanAnswerText(answer.text))
        .style({ font: { size: fontSizeDefault } });
      
      row = row + 1;
    }

    return row;
  }

  private cleanQuestionText(text: string): string {
    return text
      .replace(/<b>/g, '')
      .replace(/<\/b>/g, '')
      .replace(/<u>/g, '')
      .replace(/<\/u>/g, '')
      .replace(/<br\/>/g, '');
  }

  private cleanAnswerText(text: string): string {
    return text
      .replace(/<b>/g, '')
      .replace(/<\/b>/g, '');
  }

  private formatNotesWithAcknowledgedBy(record: any, separator: string = ' - '): string {
    // Handle undefined notes properly
    // Requirements specify: "Empty fields must be represented by a blank cell"
    if (record.notes === undefined || record.notes === null || record.notes === '') {
      return '';
    }

    const acknowledgedByPrefix = !!record?.acknowledgedBy?.username
      ? record.acknowledgedBy.username + separator
      : '';

    return acknowledgedByPrefix + record.notes;
  }

  private generatePatientInfoSheet(patient: any): string[] {
    const csvLines = [];

    // Header row (bold formatting will be handled by frontend/Excel)
    csvLines.push('Patient info');
    csvLines.push('');

    // Patient info headers
    const headers = [
      'Patient Username',
      'First Name',
      'Surname',
      'DOB',
      'Age',
      'Email address',
      'NHS Number',
      'Mobile Number',
      'Ethnicity',
      'Smoking Status',
      'Inflammatory Bowel Disease sub-type',
      'Year of diagnosis',
      'Current EDD',
      'Parity',
      'Current meds',
      'Last disease activity',
      'Previous meds',
      'Medical history/Comorbidities',
      'Last FCP (Fecal Calprotectin)'
    ];

    csvLines.push(headers.join(','));

    // Calculate age from date of birth
    const age = patient.dateOfBirth ? dayjs().diff(dayjs(patient.dateOfBirth), 'year') : '';

    // Format IBD subtype display name
    const ibdSubtypeDisplay = patient.ibdSubtype === IBDSubtype.CROHNS_DISEASE ? 'Crohn\'s disease' :
                             patient.ibdSubtype === IBDSubtype.ULCERATIVE_COLITIS ? 'Ulcerative Colitis' : '';

    // Format expected due date
    const currentEDD = patient.expectedDueDate ? dayjs(patient.expectedDueDate).utc().format('YYYY-MM-DD') : '';

    // Format year of diagnosis
    const yearOfDiagnosis = patient.yearOfDiagnosis ? dayjs(patient.yearOfDiagnosis).utc().format('YYYY-MM-DD') : '';

    // Format last disease activity
    const lastDiseaseActivity = patient.lastDiseaseActivity ? dayjs(patient.lastDiseaseActivity).utc().format('YYYY-MM-DD') : '';

    // Format last FCP with "Date of - Result" format
    let lastFcp = '';
    if (patient.lastFcpDate && patient.lastFcpResult) {
      const fcpDate = dayjs(patient.lastFcpDate).utc().format('YYYY-MM-DD');
      lastFcp = `${fcpDate} - ${patient.lastFcpResult}`;
    } else if (patient.lastFcpDate) {
      const fcpDate = dayjs(patient.lastFcpDate).utc().format('YYYY-MM-DD');
      lastFcp = `${fcpDate} - `;
    } else if (patient.lastFcpResult) {
      lastFcp = ` - ${patient.lastFcpResult}`;
    }

    // Patient data row
    const patientData = [
      patient.username || '',
      patient.firstName || '',
      patient.lastName || '',
      patient.dateOfBirth ? dayjs(patient.dateOfBirth).utc().format('YYYY-MM-DD') : '',
      age.toString(),
      patient.email || '',
      patient.nhsNumber || '',
      patient.mobileNumber || '',
      patient.ethnicity || '',
      patient.smokingStatus || '',
      ibdSubtypeDisplay,
      yearOfDiagnosis,
      currentEDD,
      patient.parity || '',
      patient.currentMeds || '',
      lastDiseaseActivity,
      patient.previousMeds || '',
      patient.medicalHistory || '',
      lastFcp
    ];

    // Escape commas and quotes in data
    const escapedData = patientData.map(field => {
      if (field.includes(',') || field.includes('"') || field.includes('\n')) {
        return `"${field.replace(/"/g, '""')}"`;
      }
      return field;
    });

    csvLines.push(escapedData.join(','));
    csvLines.push(''); // Empty line after patient info sheet

    return csvLines;
  }

  private generateIndividualPatientSymptomsSheet(patient: any, testResults: any[]): string[] {
    const csvLines = [];

    // Determine IBD subtype for question columns and sheet naming
    const ibdSubtype = patient.ibdSubtype === IBDSubtype.CROHNS_DISEASE ? 'HBI' :
                      patient.ibdSubtype === IBDSubtype.ULCERATIVE_COLITIS ? 'SCCAI' : '';

    // Generate sheet headers with test numbering
    let testCounter = 1;
    for (const testResult of testResults) {
      // Sheet header with new format: "Test [number] - [test type] - [category/subtype]"
      csvLines.push(`Test ${testCounter} - Symptoms - ${ibdSubtype}`);
      csvLines.push('');

      // Headers for symptoms sheet (removed Username and NHS number)
      const baseHeaders = [
        'Date & Time',
        'Red Flags',
        'Amber Flags',
        'Green flags',
        'Incomplete',
        'Flag',
        'Final Score',
        'Notes'
      ];

      // Add question columns based on IBD subtype
      const questionHeaders = [];
      if (ibdSubtype === 'HBI') {
        // HBI has 6 questions
        for (let i = 1; i <= 6; i++) {
          questionHeaders.push(`Q${i}`);
        }
      } else if (ibdSubtype === 'SCCAI') {
        // SCCAI has 8 questions
        for (let i = 1; i <= 8; i++) {
          questionHeaders.push(`Q${i}`);
        }
      }

      const allHeaders = [...baseHeaders, ...questionHeaders];
      csvLines.push(allHeaders.join(','));

      // Data row for this test result
      const isIncomplete = testResult.status !== TestResultStatus.COMPLETED;
      const hasFlag = testResult.redFlag || testResult.reds > 0 || testResult.ambers > 0;

      const baseData = [
        testResult.completedDate ? dayjs(testResult.completedDate).utc().format('YYYY-MM-DD HH:mm:ss') : '',
        testResult.reds || 0,
        testResult.ambers || 0,
        testResult.greens || 0,
        isIncomplete ? 'Yes' : 'No',
        hasFlag ? 'Yes' : 'No',
        testResult.totalScores || '',
        testResult?.notes || ''
      ];

      // Add question answers with human-readable text conversion
      const questionData = [];
      if (testResult.report && Array.isArray(testResult.report)) {
        const processedQuestions = this.processSymptomsQuestionsForCSV(testResult.report, ibdSubtype);
        const maxQuestions = ibdSubtype === 'HBI' ? 6 : 8;

        for (let i = 1; i <= maxQuestions; i++) {
          const questionKey = `q${i}`;
          questionData.push(processedQuestions[questionKey] || '');
        }
      } else {
        // Fill with empty values if no report data
        const maxQuestions = ibdSubtype === 'HBI' ? 6 : 8;
        for (let i = 0; i < maxQuestions; i++) {
          questionData.push('');
        }
      }

      const allData = [...baseData, ...questionData];

      // Escape commas and quotes in data
      const escapedData = allData.map(field => {
        const fieldStr = field.toString();
        if (fieldStr.includes(',') || fieldStr.includes('"') || fieldStr.includes('\n')) {
          return `"${fieldStr.replace(/"/g, '""')}"`;
        }
        return fieldStr;
      });

      csvLines.push(escapedData.join(','));
      csvLines.push(''); // Empty line after each test result

      testCounter++;
    }

    return csvLines;
  }

  private generateIndividualPatientMentalWellbeingSheet(_patient: any, testResults: any[]): string[] {
    const csvLines = [];

    // Generate sheet headers with test numbering
    let testCounter = 1;
    for (const testResult of testResults) {
      // Sheet header with new format: "Test [number] - [test type] - [category/subtype]"
      csvLines.push(`Test ${testCounter} - Mental Wellbeing - HADS`);
      csvLines.push('');

      // Headers for mental wellbeing sheet (removed Username and NHS number)
      const headers = [
        'Date & Time',
        'Incomplete',
        'Flag',
        'HADS Anxiety Score',
        'HADS Depression Score',
        'Notes'
      ];

      // Add question columns for mental wellbeing questions
      // Based on IBD-PRAM Test Questions, mental wellbeing typically has 14 questions (7 anxiety + 7 depression)
      for (let i = 1; i <= 14; i++) {
        headers.push(`Q${i}`);
      }

      csvLines.push(headers.join(','));

      // Data row for this test result
      const isIncomplete = testResult.status !== TestResultStatus.COMPLETED;
      const hasFlag = testResult.redFlag || testResult.anxietyScore >= 11 || testResult.depressionScore >= 11;

      const baseData = [
        testResult.completedDate ? dayjs(testResult.completedDate).utc().format('YYYY-MM-DD HH:mm:ss') : '',
        isIncomplete ? 'Yes' : 'No',
        hasFlag ? 'Yes' : 'No',
        testResult.anxietyScore || '',
        testResult.depressionScore || '',
        testResult?.notes || ''
      ];

      // Add question answers (up to 14 questions for mental wellbeing)
      const questionData = [];
      if (testResult.report && Array.isArray(testResult.report)) {
        for (let i = 0; i < 14; i++) {
          if (i < testResult.report.length) {
            questionData.push(testResult.report[i].answer || testResult.report[i].score || '');
          } else {
            questionData.push('');
          }
        }
      } else {
        // Fill with empty values if no report data
        for (let i = 0; i < 14; i++) {
          questionData.push('');
        }
      }

      const allData = [...baseData, ...questionData];

      // Escape commas and quotes in data
      const escapedData = allData.map(field => {
        const fieldStr = field.toString();
        if (fieldStr.includes(',') || fieldStr.includes('"') || fieldStr.includes('\n')) {
          return `"${fieldStr.replace(/"/g, '""')}"`;
        }
        return fieldStr;
      });

      csvLines.push(escapedData.join(','));
      csvLines.push(''); // Empty line after each test result

      testCounter++;
    }

    return csvLines;
  }

  private generateIndividualPatientGeneralWellbeingSheet(_patient: any, testResults: any[]): string[] {
    const csvLines = [];

    // Generate sheet headers with test numbering
    let testCounter = 1;
    for (const testResult of testResults) {
      // Sheet header with new format: "Test [number] - [test type] - [category/subtype]"
      csvLines.push(`Test ${testCounter} - General Wellbeing - SIBDQ/EQ`);
      csvLines.push('');

      // Headers for general wellbeing sheet (removed Username and NHS number)
      const baseHeaders = [
        'Date & Time',
        'Incomplete',
        'Flag',
        'Overdue Flag',
        'SIBDQ Score',
        'EQ Score',
        'Notes'
      ];

      // Add question columns for general wellbeing questions
      // Based on IBD-PRAM Test Questions, general wellbeing typically has multiple questions for SIBDQ and EQ
      for (let i = 1; i <= 37; i++) { // Assuming up to 37 questions based on typical SIBDQ (32) + EQ (5) structure
        baseHeaders.push(`Q${i}`);
      }

      // Add additional blank columns for manual clinician input as specified in requirements
      const additionalColumns = [
        '12 week scan outcomes',
        '20 week scan outcomes',
        'VTE during pregnancy',
        'VTE prophylaxis given',
        'Birth outcomes',
        'Birth weight',
        'Birth outcomes',
        'Mode of delivery',
        'Complications',
        'Gestational size',
        'Gestational age',
        'IBD outcomes',
        'Steroids during pregnancy',
        'Started biologic during pregnancy'
      ];

      const allHeaders = [...baseHeaders, ...additionalColumns];
      csvLines.push(allHeaders.join(','));

      // Data row for this test result
      const isIncomplete = testResult.status !== TestResultStatus.COMPLETED;
      const hasFlag = testResult.redFlag;
      const isOverdue = testResult.status === TestResultStatus.OVERDUE;

      const baseData = [
        testResult.completedDate ? dayjs(testResult.completedDate).utc().format('YYYY-MM-DD HH:mm:ss') : '',
        isIncomplete ? 'Yes' : 'No',
        hasFlag ? 'Yes' : 'No',
        isOverdue ? 'Yes' : 'No',
        testResult.sibdqScore || '',
        testResult.eqScore || '',
        testResult?.notes || ''
      ];

      // Add question answers with human-readable text conversion for general wellbeing
      const questionData = [];
      if (testResult.report && Array.isArray(testResult.report)) {
        const processedQuestions = this.processGeneralWellbeingQuestionsForCSV(testResult.report);
        for (let i = 1; i <= 37; i++) {
          const questionKey = `q${i}`;
          questionData.push(processedQuestions[questionKey] || '');
        }
      } else {
        // Fill with empty values if no report data
        for (let i = 0; i < 37; i++) {
          questionData.push('');
        }
      }

      // Add blank columns for manual clinician input
      const blankColumns = new Array(additionalColumns.length).fill('');

      const allData = [...baseData, ...questionData, ...blankColumns];

      // Escape commas and quotes in data
      const escapedData = allData.map(field => {
        const fieldStr = field.toString();
        if (fieldStr.includes(',') || fieldStr.includes('"') || fieldStr.includes('\n')) {
          return `"${fieldStr.replace(/"/g, '""')}"`;
        }
        return fieldStr;
      });

      csvLines.push(escapedData.join(','));
      csvLines.push(''); // Empty line after each test result

      testCounter++;
    }

    return csvLines;
  }

  private combineMultiSheetCSV(sheets: string[][]): string {
    const allLines = [];

    // Combine all sheets into a single CSV content
    for (const sheet of sheets) {
      allLines.push(...sheet);
    }

    return allLines.join('\n');
  }
}
