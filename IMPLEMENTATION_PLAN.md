# Implementation Plan: JSON Validation Range Update & Data Preservation Strategy

## Overview
This document outlines the implementation approach for two key requirements:
1. Update JSON validation range configuration to enforce maximum 3-character numeric inputs
2. Implement data preservation strategy for seed script to protect production data

## Requirement 1: JSON Validation Range Update

### Problem Statement
- Current range validation includes both `min` and `max` properties
- Maximum value was set to 9999, allowing 4-character inputs
- Need to enforce maximum of 3 characters (values < 1000)

### Solution Implemented

#### 1. Updated Test Data Files
**Files Modified:**
- `data/importer/production/test.json`
- `data/importer/test/test.json`

**Changes Made:**
- Removed `min` property from range validation objects
- Updated `max` value from 9999 to 999
- Applied to "Greater than 250" range configurations for Fecal Calprotectin tests (HBI_6 and SCCAI_8)

**Before:**
```json
"range": {
  "min": 251,
  "max": 9999
}
```

**After:**
```json
"range": {
  "max": 999
}
```

#### 2. Updated Backend Validation Logic
**File Modified:**
- `src/features/test-results/test-result.service.ts`

**Changes Made:**
- Updated `validateSpecificQuestionRules` method
- Changed maximum validation from 9999 to 999 for HBI_6 and SCCAI_8 questions
- Updated error message to reflect new limit

**Before:**
```typescript
if (value > 9999) {
  throw new BadRequestException('Value cannot exceed 9999');
}
```

**After:**
```typescript
if (value > 999) {
  throw new BadRequestException('Value cannot exceed 999');
}
```

### Impact Assessment
- ✅ Enforces 3-character maximum for numeric inputs
- ✅ Maintains data integrity for existing valid entries
- ✅ Provides clear validation feedback to users
- ✅ Consistent validation between frontend JSON and backend logic

## Requirement 2: Data Preservation Strategy for Seed Script

### Problem Statement
- Current seed script (`npm run cli seed`) destructively removes all test data
- This approach is unsuitable for production as it deletes valuable historical data
- Need migration-based approach that preserves completed test results

### Solution Implemented

#### 1. Enhanced Import Data Service
**File Modified:**
- `src/features/seed/import-data.service.ts`

**Key Changes:**

##### A. Data Preservation Logic
- Added `preserveDataAndMigrate()` method to replace destructive `clearExistingTestData()`
- Identifies and preserves completed test results before clearing data
- Uses TestResultStatus enum for proper status checking

##### B. Selective Data Clearing
- Preserves test results with status: COMPLETED or OVERDUE
- Preserves test results with valid completedDate
- Removes only incomplete/new test results (NEW or CANCELLED status)
- Removes all test and question definitions for fresh import

##### C. Post-Import Restoration
- Added `restorePreservedTestResults()` method
- Creates mapping between old and new test IDs based on test titles
- Restores preserved test results with updated test references
- Handles restoration errors gracefully with warnings

#### 2. Migration Workflow

**Step 1: Data Preservation**
```typescript
const completedTestResults = await this.testResultModel.find({
  status: { $in: [TestResultStatus.COMPLETED, TestResultStatus.OVERDUE] },
  completedDate: { $exists: true, $ne: null }
}).lean();
```

**Step 2: Selective Clearing**
```typescript
await this.testResultModel.deleteMany({
  $or: [
    { status: { $in: [TestResultStatus.NEW, TestResultStatus.CANCELLED] } },
    { completedDate: { $exists: false } },
    { completedDate: null }
  ]
});
```

**Step 3: Fresh Import**
- Import new test and question definitions
- Maintain existing user data and settings

**Step 4: Data Restoration**
- Map preserved results to new test definitions
- Restore completed test results with updated references

### Benefits of New Approach

#### Production Safety
- ✅ Preserves all completed test results and historical data
- ✅ Maintains patient progress and clinical records
- ✅ Prevents accidental data loss during updates

#### Performance Optimization
- ✅ Migration approach during seed execution (not on-the-fly)
- ✅ Batch operations for efficient database updates
- ✅ Minimal downtime during data updates

#### Data Integrity
- ✅ Maintains referential integrity between users and test results
- ✅ Preserves test completion dates and scores
- ✅ Maintains audit trail for clinical data

#### Operational Benefits
- ✅ Safe for production deployments
- ✅ Allows test definition updates without data loss
- ✅ Supports iterative development and testing

## Implementation Notes

### Error Handling
- Graceful handling of restoration failures with warning logs
- Continues processing even if individual test results fail to restore
- Comprehensive logging for monitoring and debugging

### Backward Compatibility
- Maintains existing seed script interface (`npm run cli seed`)
- No changes required to deployment procedures
- Existing development workflows remain unchanged

### Testing Considerations
- Test the migration process with sample production-like data
- Verify that completed test results are properly preserved and restored
- Confirm that new test definitions are correctly applied
- Validate that user data remains intact throughout the process

## Deployment Strategy

### Pre-Deployment
1. Backup production database
2. Test migration process in staging environment
3. Verify data preservation functionality

### Deployment
1. Deploy updated code
2. Run seed script with new preservation logic
3. Monitor logs for successful data preservation and restoration

### Post-Deployment
1. Verify that historical data is preserved
2. Confirm that new test definitions are active
3. Test that incomplete test results are properly updated

## Conclusion

This implementation provides a robust, production-safe approach to both requirements:

1. **Range Validation**: Enforces 3-character maximum for numeric inputs while maintaining consistency between frontend and backend validation
2. **Data Preservation**: Implements a migration-based strategy that preserves valuable historical data while allowing for test definition updates

The solution maintains backward compatibility while providing the safety and reliability required for production environments.
