{"Database": {"dbConfig": {"uri": ""}}, "Secret": {"jwtKey": "secret<PERSON>ey"}, "Application": {"baseUrl": "", "port": 3003, "clinicianPortalUrl": "http://64.227.44.51", "refreshTokenExpired": 36500, "tokenExpired": 36500, "timedUpPredefinedThreshold": 19, "healthStatusPredefinedThreshold": 10, "version": "v1", "filePathReport": "./files/report", "deleteExpiredTokenUserCron": "30 * * * *", "deepLinkAppStore": "https://www.apple.com/vn/app-store/", "deepLinkGoogleStore": "https://store.google.com/regionpicker"}, "Seeder": {"clinicianImportPath": "./data/importer/test/clinicians.csv", "patientImportPath": "./data/importer/test/patients.csv", "patientResourcePath": "./data/importer/test/patient-resources.json", "testDataPath": "./data/importer/test/test.json", "clinicianExportPath": "./data/exporter/test/clinicians.csv", "patientExportPath": "./data/exporter/test/patients.csv"}, "SendGrid": {"apiKey": "*********************************************************************", "sender": "<EMAIL>", "senderName": "IBD-PRAM"}, "Smtp": {"email": "<EMAIL>", "url": "smtp.gmail.com", "port": 465, "user": "<EMAIL>", "pass": "lpnp jbwx xzgx fovq", "senderName": "IBD-PRAM"}, "Firebase": {"databaseURL": "https://ibd-pram-default-rtdb.europe-west1.firebasedatabase.app/"}, "Cron": {"testOverDate": "0 0 0 * * *", "timezone": "Asia/Ho_Chi_Minh"}}