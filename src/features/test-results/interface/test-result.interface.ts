import { Types } from 'mongoose';

export interface TestResultRO {
  id: Types.ObjectId;
  title: string;
  reds: string[];
  greens: string[];
  ambers: string[];
  totalScores: number;
  ttc: number;
  notes: string;
  redFlag: boolean;
  acknowledged: boolean;
  status: string;
  startDate: string;
  completedDate: string;
  dueDate: string;
}

export interface TestResultsWithPagination {
  items: TestResultRO[];
  totalDocs: number;
  limit: number;
  totalPages: number;
  page: number;
  baseScore?: number;
}

export interface TestMentalWellbeingRO {
  id: Types.ObjectId;
  anxietyScore: number;
  depressionScore: number;
  completedDate: string;
}

export interface TestHealthStatusRO {
  id: Types.ObjectId;
  score: number;
  completedDate: string;
}

export interface UpdateData {
  detail: {
    questions: RequestQuestion[];
  };
  completedDate: Date
  completed?: boolean;
  cancelled?: boolean;
}

export interface RequestQuestion {
  id?: string;
  text?: string;
  code?: string;
  order?: number;
  type: string;
  unitText?: string;
  maxLength?: number;
  numericValue?: number;
  textValue?: string;
  testDate?: string;
  answers?: RequestAnswer[];
}

export interface RequestAnswer {
  id: string;
  text?: string;
  code?: string;
  score?: number;
  rag?: string;
  selected?: boolean;
  textValue?: string;
  numericValue?: number;
  testDate?: string;
  questions?: RequestQuestion[];
}

export interface DatabaseQuestion {
  _id?: string;
  text?: string;
  code?: string;
  order?: number;
  type: string;
  questions?: DatabaseQuestion[];
  answers?: DatabaseAnswer[];
  createdAt?: Date;
}

export interface DatabaseAnswer {
  id?: string;
  text?: string;
  code?: string;
  score?: number;
  anxietyScore?: number;
  depressionScore?: number;
  sibdqScore?: number;
  eqScore?: number;
  rag?: string;
  additionalNote?: string;
  point?: number;
  questions?: DatabaseQuestion[];
}

export interface ReportItem {
  code: string;
  question: string;
  answer: string;
  score: number;
  rag?: string;
  redFlag: boolean;
  order: number;
  numericValue?: number;
  textValue?: string;
  testDate?: string;
  // Optional HADS scores for Mental Wellbeing tests
  anxietyScore?: number;
  depressionScore?: number;
  // Optional scores for General Wellbeing tests
  sibdqScore?: number;
  eqScore?: number;
}

export interface SelectedAnswerData {
  id: string;
  selected: boolean;
  textValue?: string;
  numericValue?: number;
  testDate?: string;
}

export interface PatientDetailsWithPagination {
  data: PatientTestResultRO[];
  metadata: PaginationMetadata;
}

export interface PatientTestResultRO {
  id: string;
  title: string;
  patient: PatientInfo;
  acknowledgedBy: AcknowledgedByInfo | null;
  reds: number;
  greens: number;
  ambers: number;
  totalScores: string;
  ttc?: number;
  anxietyScore?: number;
  depressionScore?: number;
  sibdqScore?: number;
  eqScore?: number;
  notes?: string;
  redFlag: boolean;
  acknowledged: boolean;
  status: string;
  startDate: number;
  completedDate: number;
  dueDate: number;
  flagDetails?: FlagDetailRO[];
  ragBreakdown?: RAGBreakdownRO;
}

export interface FlagDetailRO {
  flagType: string;
  flagReason: string;
  flaggedAt: string;
  redFlagAcknowledged: boolean;
  redFlagAcknowledgedBy?: AcknowledgedByInfo;
  redFlagAcknowledgedAt?: string;
  redFlagNotes?: string;
  questionNumbers?: string[];
  testSubtype?: string;
}

export interface RAGBreakdownRO {
  redQuestions: QuestionBreakdownRO[];
  amberQuestions: QuestionBreakdownRO[];
  greenQuestions: QuestionBreakdownRO[];
}

export interface QuestionBreakdownRO {
  questionNumber: string;
  ibdSubtype: string;
  displayText: string;
}

export interface PatientInfo {
  id: string;
  username: string;
  firstName: string;
  lastName: string;
  fullName: string;
  ibdSubtype?: string;
}

export interface AcknowledgedByInfo {
  id: string;
  firstName: string;
  lastName: string;
  fullName: string;
}

export interface PaginationMetadata {
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}
